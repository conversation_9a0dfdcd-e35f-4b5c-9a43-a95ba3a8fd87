"use client";
import React, { useState, useEffect, useContext,useCallback } from 'react';
import ProjectList from './ProjectList';
import { fetchProjectsList, fetchNodeById ,fetchProjectCreators} from '../../utils/api';
import { fetchPublicProjects } from '../../utils/projectApi';
import { SideBarContext } from '../Context/SideBarContext';
import { TopBarContext } from '../Context/TopBarContext';
import { buildProjectUrl } from '@/utils/navigationHelpers';

import { useRouter, usePathname } from 'next/navigation';
import {ProjectListSkeleton} from "@/components/UIComponents/Loaders/LoaderGroup"
import CreateProjectModal from '../Modal/CreateProjectModal';
import ErrorView from '../Modal/ErrorViewModal';
import Cookies from 'js-cookie';
import { createPortal } from 'react-dom';

import en from "../../en.json";

import '@/styles/chatlist.css';
import { useUser } from '../Context/UserContext';

const getProjectIdFromPath = (pathname: any) => {
  const pathParts = pathname.split('/');
  return pathParts.length > 3 ? pathParts[3] : null;
};

interface PreloadProjectListProps {
  handleDrawerToggle: () => void;
  handleDrawerClose: () => void;
  theme?: 'light' | 'dark';
}

const PreloadProjectList: React.FC<PreloadProjectListProps> = ({
  handleDrawerToggle,
  handleDrawerClose,
  theme: propTheme = 'light'
}) => {
  const { prevProjects, setPrevProjects, closeSidebar } = useContext(SideBarContext);
  const { tabs, addTab, setActiveTab: setTopBarActiveTab, updateTabTitle, updateProjectInfo } = useContext(TopBarContext);

  // Use prop theme (light by default for all pages except home)
  const theme = propTheme;

const PAGE_SIZE = 10;
  const [projects, setProjects] = useState(prevProjects);
  const [privateProjects, setPrivateProjects] = useState<any[]>([]);
  const [privatePagination, setPrivatePagination] = useState({
    total_count: 0,
    total_pages: 0,
    current_page: 1,
    page_size: PAGE_SIZE,
    has_next: false,
    has_previous: false,
    start_index: 0,
    end_index: 0
  });
  const [privateCreators, setPrivateCreators] = useState<string[]>([]);
  const [publicProjects, setPublicProjects] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'private' | 'public'>('private');
  const [isLoading, setIsLoading] = useState(true);
  const [isPublicLoading, setIsPublicLoading] = useState(true);
  const [error, setError] = useState(null);
  const pathname = usePathname();
  const router = useRouter();
  const [currentProjectId, setCurrentProjectId] = useState(getProjectIdFromPath(pathname));
  const [isProjectModalOpen, setIsProjectModalOpen] = useState(false);
  const { is_public_project_selected } = useUser();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCreator, setSelectedCreator] = useState('')
   const [isSearching, setIsSearching] = useState(false);

 // Debounced search hook
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      // Don't debounce empty values - clear immediately
      if (!value.trim()) {
        setDebouncedValue(value);
        return;
      }

      // Only debounce when user is actively typing
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  const debouncedSearchTerm = useDebounce(searchTerm, 1600);

  // Track when user is typing vs when search is debounced
// Track user is typing
useEffect(() => {
  const isUserTyping = searchTerm !== debouncedSearchTerm && searchTerm.trim().length >= 3;
  setIsSearching(isUserTyping);
}, [searchTerm, debouncedSearchTerm]);


  // Fetch private projects with pagination
  const fetchPrivateProjects = useCallback(async (page = 1, search = '', creator = '') => {
    setError(null);
    setIsLoading(true);
    try {
      const result = await fetchProjectsList(page, PAGE_SIZE, search, creator);
      setPrivateProjects(result?.data?.projects);
      setPrivatePagination(result?.data?.pagination);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch creators for filtering
  const fetchCreators = useCallback(async () => {
    try {
      const creators = await fetchProjectCreators();
      setPrivateCreators(creators);
    } catch (err) {
    }
  }, []);
  // Function to refresh projects after clone
  const refreshProjectsList = () => {
    if (activeTab === 'private') {
      fetchPrivateProjects();
        fetchCreators();
    } else {
      fetchAllPublicProjects();
    }
  };

  const fetchAllPublicProjects = async () => {
    setError(null);
    setIsPublicLoading(true);
    try {
      const data = await fetchPublicProjects();
      // Transform public projects to match the format expected by ProjectList
      if (data && typeof data === 'object' && 'public_projects' in data && Array.isArray(data.public_projects)) {
        const formattedPublicProjects = data.public_projects.map((project: any) => ({
          id: project.project_id || project.id,
          Title: project.title || project.Title,
          Description: project.description,
          CreatedAt: project.created_at,
          CreatedBy: project.created_by,
          creator_name: project.creator_name || 'Unknown',
          creator_email: project.creator_email,
          creator_picture: project.creator_picture,
          created_at: project.created_at,
          tenant_id: project.tenant_id,
          isPublic: true,
        }));

        // Sort by creation date in descending order (newest first)
        const sortedPublicProjects = formattedPublicProjects.sort((a, b) => {
          const dateA = new Date(a.created_at || a.CreatedAt).getTime();
          const dateB = new Date(b.created_at || b.CreatedAt).getTime();
          return dateB - dateA; // descending order
        });

        setPublicProjects(sortedPublicProjects);

      } else {
        setPublicProjects([]);
      }
    } catch (err: any) {
      setError(err);
    } finally {
      setIsPublicLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'private') {
      fetchPrivateProjects();
      fetchCreators();
    } else {
      fetchAllPublicProjects();
    }
  }, [activeTab, setPrevProjects,fetchPrivateProjects, fetchCreators]);

    // Handle search and filter changes for private projects
 // Handle search and filter changes for private projects (debounced)
useEffect(() => {
  if (activeTab === 'private') {
    // If term is less than 3 characters, skip filtering
    if (debouncedSearchTerm.trim().length < 3 && debouncedSearchTerm !== '') {
      setPrivateProjects([]); // Optionally show "no results"
      setIsSearching(false);
      return;
    }

    // Start searching
    setIsSearching(true);
    fetchPrivateProjects(1, debouncedSearchTerm, selectedCreator)
      .finally(() => setIsSearching(false));
  }
}, [debouncedSearchTerm, selectedCreator, activeTab, fetchPrivateProjects]);

  // Handle pagination changes
  const handlePageChange = useCallback((page: number) => {
    if (activeTab === 'private' && page >= 1 && page <= privatePagination.total_pages) {
      fetchPrivateProjects(page, debouncedSearchTerm, selectedCreator);
    }
  }, [activeTab, privatePagination?.total_pages, fetchPrivateProjects, debouncedSearchTerm, selectedCreator]);

  const handleSearchChange = (value: string) => {
  setSearchTerm(value);

  if (!value.trim()) {
    setPrivateProjects([]);
    setIsSearching(false);
  }
};


  useEffect(() => {
    const newProjectId = getProjectIdFromPath(pathname);
    if (newProjectId !== currentProjectId) {
      setCurrentProjectId(newProjectId);
    }
  }, [pathname, currentProjectId]);

  const handleProjectClick = async (project: any) => {
    // Store project information
    const isPublic = activeTab === 'public';
    const projectInfo = {
      selected_project_id: project.id.toString(),
      selected_project_creator_email: project.creator_email || '',
      is_public_selected: isPublic,  // true for Shared tab, false for My Projects
      selected_tenant_id: (activeTab === 'public' || project.isPublic) ? project.tenant_id : Cookies.get('tenant_id') || ''
    };

    // Keep cookies for backward compatibility
    Cookies.set('selected_project_id', projectInfo.selected_project_id);
    Cookies.set('selected_project_creator_email', projectInfo.selected_project_creator_email);
    Cookies.set('is_public_selected', isPublic.toString());  // Convert boolean to string
    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

    const projectRes = await fetchNodeById(project.id, "Project");
    let title = projectRes.properties?.Title || projectRes.properties?.Name || "Project"

    const projectUrl = buildProjectUrl(project.id, 'overview');

    const existingTab = tabs.find((tab: any) => {
      const tabProjectId = tab.href.split('/')[3]; // Extract project ID from href
      return tabProjectId === project.id.toString(); // Compare with current project ID
    });

    if (existingTab) {
      if (existingTab.href !== projectUrl) {
        updateTabTitle(project.id, title, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(existingTab.href);
      }
      else {
        // Update project info even if the href is the same
        updateProjectInfo(project.id, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(projectUrl);
      }
    }
    else {
      const { buildProjectUrl } = require('@/utils/navigationHelpers');
      const newProjectUrl = buildProjectUrl(project.id, 'overview');
      addTab(title, newProjectUrl, projectInfo);
      router.push(newProjectUrl);
    }
    closeSidebar();
    handleDrawerToggle();
  };

  const createNewProjectAndCloseSidebar = () => {
    setIsProjectModalOpen(true);
    handleDrawerToggle();
  };

  const openProjectModal = () => {
    setIsProjectModalOpen(true);
  };

  const handleCloseModal = async () => {

      try {
        const createdProjectIds = JSON.parse(localStorage.getItem('createdProjectIds') || '[]');
        const projectId = createdProjectIds[createdProjectIds.length - 1];
        // Create project info object
        const projectInfo = {
          selected_project_id: projectId.toString(),
          selected_project_creator_email: '',
          is_public_selected: false, // New projects are private by default
          selected_tenant_id: Cookies.get('tenant_id') || ''
        };

        // Store in cookies for backward compatibility
        Cookies.set('selected_project_id', projectInfo.selected_project_id);
        Cookies.set('is_public_selected', projectInfo.is_public_selected.toString());
        Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

        // Get project details and create URL
        const projectRes = await fetchNodeById(projectId, "Project");
        const projectName = projectRes.properties?.Title || projectRes.properties?.Name || "Project";
        const newProjectUrl = buildProjectUrl(projectId, 'overview');

        // Check if tab already exists for this project
        const existingTab = tabs.find((tab: any) => {
          const tabProjectId = tab.href.split('/')[3];
          return tabProjectId === projectId.toString();
        });

        if (existingTab) {
          // Tab exists - update and navigate to it
          updateProjectInfo(projectId, projectInfo);
          setTopBarActiveTab(existingTab.id);
          router.push(existingTab.href);
        } else {
          // Tab doesn't exist - create new tab
          addTab(projectName, newProjectUrl, projectInfo);
          router.push(newProjectUrl);
        }

      } catch (error) {
        console.error('Error handling project creation:', error);
      }
 
    setIsProjectModalOpen(false);
  };

  const handleUpdateResponse = (response: any) => {
    // Create project info object
    const projectInfo = {
      selected_project_id: response.id.toString(),
      selected_project_creator_email: '',
      is_public_selected: 'false', // New projects are private by default
      selected_tenant_id: Cookies.get('tenant_id') || ''
    };

    // Store in cookies for backward compatibility
    Cookies.set('selected_project_id', projectInfo.selected_project_id);
    Cookies.set('is_public_selected', projectInfo.is_public_selected);
    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

    // Update projects state
    setProjects([...projects, response]);
    setPrevProjects([...projects, response]);
    setIsProjectModalOpen(false); // Close the modal
    closeSidebar(); // Close the sidebar
    handleDrawerToggle(); // Toggle the drawer state

    // Navigate to the new project
    const newProjectUrl = buildProjectUrl(response.id, 'overview');
    const projectName = response.properties.Title || response.properties.Name;

    const existingTab = tabs.find((tab: any) => {
      const tabProjectId = tab.href.split('/')[3]; // Extract project ID from href
      return tabProjectId === response.id.toString(); // Compare with current project ID
    });

    if (existingTab) {
      if (existingTab.href !== newProjectUrl) {
        updateTabTitle(response.id, projectName, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(existingTab.href);
      }
      else {
        updateProjectInfo(response.id, projectInfo);
        setTopBarActiveTab(existingTab.id);
        router.push(newProjectUrl);
      }
    }
    else {
      const finalProjectUrl = buildProjectUrl(response.id, 'overview');
      addTab(projectName, finalProjectUrl, projectInfo);
      router.push(finalProjectUrl);
    }
  };

  const handleTabChange = (tab: 'private' | 'public') => {
    setActiveTab(tab);
  };

  // Theme classes
  const themeClasses = {
    light: {
      container: "bg-custom-bg-primary text-custom-text-primary h-full flex flex-col overflow-hidden",
      tabContainer: "project-tabs flex-shrink-0 z-10 bg-custom-bg-primary border-b border-custom-border",
      tab: "project-tab",
      activeTab: "project-tab active",
      contentWrapper: "w-full flex-1 bg-custom-bg-primary overflow-hidden",
      errorContainer: "preload-chat-list bg-custom-bg-primary h-full"
    },
    dark: {
      container: "bg-custom-bg-primary text-custom-text-primary h-full flex flex-col overflow-hidden",
      tabContainer: "project-tabs flex-shrink-0 z-10 bg-custom-bg-primary border-b border-custom-border",
      tab: "project-tab text-custom-text-primary hover:text-custom-text-primary",
      activeTab: "project-tab active text-custom-text-primary",
      contentWrapper: "w-full flex-1 bg-custom-bg-primary overflow-hidden",
      errorContainer: "preload-chat-list bg-custom-bg-primary h-full"
    }
  };

  if (error) {
    return (
      <div className={themeClasses[theme].errorContainer}>
        <ErrorView
          title="Unable to Load Projects"
          message={en.ChatList_ErrorLoadingChats}
          showRetryButton={true}
          onRetry={() => activeTab === 'private' ? fetchPrivateProjects : fetchAllPublicProjects}
        />
      </div>
    );
  }

  return (
    <div className={themeClasses[theme].container}>
      <div className={themeClasses[theme].tabContainer}>
        <button
          className={activeTab === 'private' ? themeClasses[theme].activeTab : themeClasses[theme].tab}
          onClick={() => handleTabChange('private')}

        >
          My Projects
        </button>
        <button
          className={activeTab === 'public' ? themeClasses[theme].activeTab : themeClasses[theme].tab}
          onClick={() => handleTabChange('public')}

        >
          Shared
        </button>
      </div>
      <div className={themeClasses[theme].contentWrapper}>
        {isLoading && activeTab === 'private' ? (
          <ProjectListSkeleton theme={theme} />
        ) : isPublicLoading && activeTab === 'public' ? (
          <ProjectListSkeleton theme={theme} />
        ) : (
          <ProjectList
            projects={activeTab === 'private' ? projects : publicProjects}
            onProjectClick={handleProjectClick}
            selectedProjectId={currentProjectId}
            closeSidebar={closeSidebar}
            openProjectModal={openProjectModal}
            isSharedTab={activeTab === 'public'}
            refreshProjectsList={refreshProjectsList}
            theme={theme}
            projectsData={activeTab === 'private' ? privateProjects : publicProjects}
            pagination={activeTab === 'private' ? privatePagination : null}
            creators={activeTab === 'private' ? privateCreators : []}
            onPageChange={handlePageChange}
            searchTerm={searchTerm}
            onSearchChange={handleSearchChange}
            selectedCreator={selectedCreator}
            onCreatorChange={setSelectedCreator}
            loading={activeTab === 'private' ? isLoading : isPublicLoading}
            isSearching={isSearching}
            onDrawerClose={handleDrawerClose}
          />
        )}
      </div>
      {isProjectModalOpen && (
        typeof window !== 'undefined' && createPortal(
          <CreateProjectModal
            isOpen={isProjectModalOpen}
            onClose={handleCloseModal}
            onUpdateResponse={handleUpdateResponse}
            type="Project"
          />,
          document.body
        )
      )}
    </div>
  );
};

PreloadProjectList.displayName = 'PreloadProjectList';
export default PreloadProjectList;